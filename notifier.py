"""
Notifier Module for SecureFolder Pro

Handles sending email alerts for events like unauthorized access attempts.
"""

import smtplib
import json
from email.mime.text import MIMEText
from datetime import datetime

CONFIG_FILE = 'config.json'

def load_email_config() -> dict | None:
    """Loads email configuration from config.json."""
    try:
        with open(CONFIG_FILE, 'r') as f:
            config = json.load(f)
            return config.get('email_config')
    except FileNotFoundError:
        print(f"Error: {CONFIG_FILE} not found.")
        return None
    except json.JSONDecodeError:
        print(f"Error: {CONFIG_FILE} is corrupted.")
        return None

def send_alert_email(subject: str, body_message: str, user_info: str = "N/A") -> bool:
    """Sends an email alert.

    Args:
        subject: The subject of the email.
        body_message: The main content of the email.
        user_info: Information about the user/attempt (e.g., IP, username if available).

    Returns:
        True if the email was sent successfully, False otherwise.
    """
    email_conf = load_email_config()
    if not email_conf:
        print("Email configuration not found. Cannot send alert.")
        return False

    smtp_server = email_conf.get('smtp_server')
    smtp_port = email_conf.get('smtp_port')
    sender_email = email_conf.get('sender_email')
    sender_password = email_conf.get('sender_password')
    recipient_email = email_conf.get('recipient_email')

    if not all([smtp_server, smtp_port, sender_email, sender_password, recipient_email]):
        print("Email configuration is incomplete. Please check smtp_server, smtp_port, sender_email, sender_password, and recipient_email in config.json.")
        return False

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    full_body = f"Alert from SecureFolder Pro\n"
    full_body += f"Timestamp: {timestamp}\n"
    full_body += f"User/Attempt Info: {user_info}\n\n"
    full_body += f"Details:\n{body_message}"

    msg = MIMEText(full_body)
    msg['Subject'] = f"[SecureFolderPro Alert] {subject}"
    msg['From'] = sender_email
    msg['To'] = recipient_email

    try:
        print(f"Attempting to send email alert to {recipient_email} via {smtp_server}:{smtp_port}...")
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.ehlo()  # Extended Hello
            server.starttls()  # Enable TLS encryption
            server.ehlo()  # Re-send EHLO after starting TLS
            server.login(sender_email, sender_password)
            server.sendmail(sender_email, recipient_email, msg.as_string())
            print("Email alert sent successfully.")
            return True
    except smtplib.SMTPAuthenticationError:
        print(f"SMTP Authentication Error: Could not log in with {sender_email}. Check credentials or 'less secure app access' if using Gmail.")
        return False
    except smtplib.SMTPServerDisconnected:
        print("SMTP Server Disconnected: The server unexpectedly disconnected.")
        return False
    except smtplib.SMTPConnectError:
        print(f"SMTP Connect Error: Could not connect to {smtp_server}:{smtp_port}.")
        return False
    except ConnectionRefusedError:
        print(f"Connection Refused: Could not connect to {smtp_server}:{smtp_port}. Ensure the server is running and accessible.")
        return False
    except TimeoutError:
        print(f"Timeout Error: Connection to {smtp_server}:{smtp_port} timed out.")
        return False
    except Exception as e:
        print(f"Failed to send email alert: {e}")
        return False

def notify_failed_login_attempt(username_attempted: str = "Unknown", ip_address: str = "N/A"):
    """Sends a notification for a failed login attempt."""
    config = load_email_config() # Check if email notifications are enabled implicitly by config presence
    if not config or not config.get('smtp_server'): # Basic check if email is configured
        print("Email notifications for failed logins are not configured.")
        return

    subject = "Failed Login Attempt Detected"
    body = (
        f"A failed login attempt to SecureFolder Pro was detected.\n"
        f"Username attempted: {username_attempted}\n"
        f"IP Address (if available): {ip_address}"
    )
    send_alert_email(subject, body, user_info=f"Username: {username_attempted}, IP: {ip_address}")

if __name__ == '__main__':
    print("--- Notifier Module Test ---")
    
    # IMPORTANT: For this test to work, you MUST:
    # 1. Have a valid config.json with correct email_config details.
    # 2. Use an email provider that allows SMTP access.
    # 3. For Gmail, you might need to enable "Less secure app access" or generate an "App Password".
    #    (Using App Passwords is more secure than enabling less secure app access).

    email_conf_test = load_email_config()
    if not email_conf_test or not email_conf_test.get('smtp_server') or not email_conf_test.get('recipient_email'):
        print("Email configuration in 'config.json' is missing or incomplete.")
        print("Please provide: smtp_server, smtp_port, sender_email, sender_password, recipient_email.")
        print("Skipping direct email sending test.")
    else:
        print("Testing direct email sending function...")
        test_subject = "Test Alert from SecureFolderPro Notifier"
        test_body = "This is a test email sent from the notifier.py script."
        test_user_info = "Test User, IP: 127.0.0.1"
        
        success = send_alert_email(test_subject, test_body, test_user_info)
        if success:
            print("Direct email send test SUCCEEDED (check recipient's inbox).")
        else:
            print("Direct email send test FAILED. Check console output for errors and verify email settings in config.json.")

    print("\nTesting failed login notification function...")
    # This will attempt to send an email if configured
    notify_failed_login_attempt(username_attempted="test_user_fail", ip_address="*************")
    print("If email is configured, an alert for 'Failed Login Attempt' should have been sent.")

    print("\nNotifier module tests finished.")