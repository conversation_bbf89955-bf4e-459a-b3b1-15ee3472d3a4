"""
SecureFolder Pro - Main Application

GUI entry point using PyQt6 for folder encryption, decryption, and management.
"""

import sys
import os
import json
import shutil # For moving/deleting folders
import subprocess # For attrib command
import time
import qrcode

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QW<PERSON>t, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QLineEdit, QFileDialog, QMessageBox,
    QProgressBar, QDialog, QGridLayout, QGroupBox
)
from PyQt6.QtGui import QPixmap, QImage, QIcon
from PyQt6.QtCore import Qt, QThread, pyqtSignal

# Project Modules
import crypto_utils
import auth
import usb_key
import notifier

CONFIG_FILE = 'config.json'
APP_NAME = "SecureFolder Pro"
VERSION = "1.0.0"

# --- Utility Functions ---
def load_app_config():
    """Loads the main application configuration."""
    try:
        with open(CONFIG_FILE, 'r') as f:
            return json.load(f)
    except Exception as e:
        QMessageBox.critical(None, "Config Error", f"Could not load {CONFIG_FILE}: {e}")
        return None

def save_app_config(config_data):
    """Saves the main application configuration."""
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config_data, f, indent=4)
    except Exception as e:
        QMessageBox.critical(None, "Config Error", f"Could not save {CONFIG_FILE}: {e}")

# --- Worker Threads for Long Operations ---
class EncryptDecryptThread(QThread):
    """Worker thread for encryption/decryption to keep GUI responsive."""
    progress = pyqtSignal(int)
    finished = pyqtSignal(str, bool) # Message, is_success
    error = pyqtSignal(str)

    def __init__(self, folder_path, master_key, operation_type, config):
        super().__init__()
        self.folder_path = folder_path
        self.master_key = master_key # This should be the raw key material for crypto_utils
        self.operation_type = operation_type # "encrypt" or "decrypt"
        self.config = config
        self.encrypted_folder_suffix = "_encrypted"
        self.metadata_filename = ".folder_metadata"

    def run(self):
        try:
            if self.operation_type == "encrypt":
                self._encrypt_folder()
            elif self.operation_type == "decrypt":
                self._decrypt_folder()
            else:
                self.error.emit("Invalid operation type.")
        except Exception as e:
            self.error.emit(f"An unexpected error occurred: {str(e)}")

    def _encrypt_folder(self):
        self.progress.emit(0)
        original_folder_name = os.path.basename(self.folder_path)
        parent_dir = os.path.dirname(self.folder_path)
        encrypted_folder_path = os.path.join(parent_dir, original_folder_name + self.encrypted_folder_suffix)

        if os.path.exists(encrypted_folder_path):
            self.error.emit(f"Encrypted folder '{encrypted_folder_path}' already exists. Please remove or rename it.")
            return
        
        os.makedirs(encrypted_folder_path, exist_ok=True)
        self.progress.emit(5)

        file_metadata = {}
        files_to_encrypt = []
        for root, _, files in os.walk(self.folder_path):
            for file in files:
                files_to_encrypt.append(os.path.join(root, file))
        
        if not files_to_encrypt:
            self.finished.emit("Folder is empty. Nothing to encrypt.", True)
            # Clean up empty encrypted folder if created
            if os.path.exists(encrypted_folder_path) and not os.listdir(encrypted_folder_path):
                os.rmdir(encrypted_folder_path)
            return

        total_files = len(files_to_encrypt)
        buffer_size = self.config.get('encryption_buffer_size', 65536)

        for i, file_path in enumerate(files_to_encrypt):
            try:
                original_filename = os.path.basename(file_path)
                obfuscated_filename = crypto_utils.obfuscate_filename(original_filename, self.master_key)
                file_metadata[obfuscated_filename] = original_filename
                
                relative_path = os.path.relpath(file_path, self.folder_path)
                encrypted_file_dir = os.path.join(encrypted_folder_path, os.path.dirname(relative_path))
                os.makedirs(encrypted_file_dir, exist_ok=True)
                
                # Encrypt the file itself
                # The crypto_utils.encrypt_file expects the output path to be input_path + '.enc'
                # We need to manage the final path and naming ourselves here.
                temp_encrypted_path = crypto_utils.encrypt_file(file_path, self.master_key, buffer_size)
                
                final_encrypted_path = os.path.join(encrypted_file_dir, obfuscated_filename + ".enc")
                shutil.move(temp_encrypted_path, final_encrypted_path)

                self.progress.emit(int(5 + (i + 1) / total_files * 90))
            except Exception as e:
                self.error.emit(f"Error encrypting {file_path}: {e}")
                # Consider cleanup or rollback strategy here
                return

        # Save metadata
        metadata_file_path = os.path.join(encrypted_folder_path, self.metadata_filename)
        try:
            with open(metadata_file_path, 'w') as mf:
                json.dump(file_metadata, mf, indent=4)
        except Exception as e:
            self.error.emit(f"Failed to save metadata: {e}")
            return

        self.progress.emit(98)
        # Hide original folder (Windows specific example)
        try:
            if os.name == 'nt':
                subprocess.run(['attrib', '+h', self.folder_path], check=True, shell=True, creationflags=subprocess.CREATE_NO_WINDOW)
            # For other OS, might use 'chflags hidden' on macOS or rename with '.' prefix on Linux
            # Or simply move it to a hidden archive location
            # For simplicity, we'll just inform the user.
            # A more robust solution would be to move the original folder to a temporary secure location
            # and delete it only after successful verification of decryption.
            # For now, we just hide it.
        except Exception as e:
            self.error.emit(f"Could not hide original folder '{self.folder_path}': {e}. Please secure it manually.")
            # Don't stop the whole process for this

        self.progress.emit(100)
        self.finished.emit(f"Folder '{original_folder_name}' encrypted successfully to '{os.path.basename(encrypted_folder_path)}'. Original folder hidden.", True)

    def _decrypt_folder(self):
        self.progress.emit(0)
        if not self.folder_path.endswith(self.encrypted_folder_suffix):
            self.error.emit(f"Selected folder '{os.path.basename(self.folder_path)}' does not appear to be an encrypted folder (missing '{self.encrypted_folder_suffix}' suffix).")
            return

        original_folder_name_encrypted = os.path.basename(self.folder_path)
        original_folder_name_decrypted = original_folder_name_encrypted.replace(self.encrypted_folder_suffix, "")
        parent_dir = os.path.dirname(self.folder_path)
        decrypted_folder_path = os.path.join(parent_dir, original_folder_name_decrypted)

        if os.path.exists(decrypted_folder_path):
            # Check if it's the hidden original or another folder
            is_hidden_original = False
            if os.name == 'nt':
                try:
                    attrs = subprocess.check_output(['attrib', decrypted_folder_path], shell=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
                    if ' H ' in attrs.splitlines()[0]: # Check if Hidden attribute is set
                        is_hidden_original = True
                except Exception:
                    pass # Can't determine attributes, assume not hidden original
            
            if is_hidden_original:
                # Unhide it first
                try:
                    if os.name == 'nt':
                        subprocess.run(['attrib', '-h', decrypted_folder_path], check=True, shell=True, creationflags=subprocess.CREATE_NO_WINDOW)
                    self.finished.emit(f"Original folder '{original_folder_name_decrypted}' already exists and was unhidden. No decryption performed.", True)
                    # Optionally, delete the _encrypted folder here if desired
                    # shutil.rmtree(self.folder_path)
                    return
                except Exception as e:
                    self.error.emit(f"Original folder '{original_folder_name_decrypted}' exists but could not be unhidden: {e}. Please manage manually.")
                    return
            else:
                self.error.emit(f"Decrypted folder destination '{decrypted_folder_path}' already exists. Please remove or rename it.")
                return

        # Load metadata
        metadata_file_path = os.path.join(self.folder_path, self.metadata_filename)
        if not os.path.exists(metadata_file_path):
            self.error.emit(f"Metadata file '{self.metadata_filename}' not found in '{original_folder_name_encrypted}'. Cannot decrypt.")
            return
        
        try:
            with open(metadata_file_path, 'r') as mf:
                file_metadata = json.load(mf)
        except Exception as e:
            self.error.emit(f"Failed to load or parse metadata: {e}")
            return

        os.makedirs(decrypted_folder_path, exist_ok=True)
        self.progress.emit(5)

        files_to_decrypt = []
        for root, _, files in os.walk(self.folder_path):
            for file in files:
                if file == self.metadata_filename: # Skip metadata file itself
                    continue
                files_to_decrypt.append(os.path.join(root, file))
        
        if not files_to_decrypt:
            self.finished.emit("Encrypted folder is empty (or only contains metadata). Nothing to decrypt.", True)
            # Clean up empty decrypted folder if created
            if os.path.exists(decrypted_folder_path) and not os.listdir(decrypted_folder_path):
                os.rmdir(decrypted_folder_path)
            return

        total_files = len(files_to_decrypt)
        buffer_size = self.config.get('encryption_buffer_size', 65536)

        for i, enc_file_path in enumerate(files_to_decrypt):
            try:
                obfuscated_filename_with_ext = os.path.basename(enc_file_path)
                obfuscated_filename = obfuscated_filename_with_ext.rsplit('.enc', 1)[0]
                
                original_filename = file_metadata.get(obfuscated_filename)
                if not original_filename:
                    self.error.emit(f"Original filename not found in metadata for '{obfuscated_filename_with_ext}'. Skipping.")
                    continue

                relative_enc_path = os.path.relpath(os.path.dirname(enc_file_path), self.folder_path)
                decrypted_file_dir = os.path.join(decrypted_folder_path, relative_enc_path)
                os.makedirs(decrypted_file_dir, exist_ok=True)
                
                # Decrypt the file
                # crypto_utils.decrypt_file expects the output path to be input_path without .enc
                temp_decrypted_path = crypto_utils.decrypt_file(enc_file_path, self.master_key, buffer_size)
                
                final_decrypted_path = os.path.join(decrypted_file_dir, original_filename)
                shutil.move(temp_decrypted_path, final_decrypted_path)

                self.progress.emit(int(5 + (i + 1) / total_files * 90))
            except ValueError as ve: # Specific error from deobfuscate_filename or decrypt_file (e.g. InvalidTag)
                self.error.emit(f"Decryption error for {enc_file_path} (likely wrong password or corrupted file): {ve}")
                # Critical error, stop and recommend rollback/check
                # For now, we stop and the user has to manage the partially decrypted folder.
                # A robust solution would offer to clean up the partially decrypted folder.
                return
            except Exception as e:
                self.error.emit(f"Error decrypting {enc_file_path}: {e}")
                return

        self.progress.emit(98)
        # Delete the encrypted folder after successful decryption
        try:
            shutil.rmtree(self.folder_path)
        except Exception as e:
            self.error.emit(f"Could not delete encrypted folder '{original_folder_name_encrypted}': {e}. Please remove it manually.")
            # Don't let this fail the whole operation's success message

        # Unhide the original folder if it was previously hidden (this logic is now at the start of decrypt)
        # If the decrypted_folder_path was newly created, it won't be hidden.
        # If it was an existing hidden folder, it's unhidden at the start.

        self.progress.emit(100)
        self.finished.emit(f"Folder '{original_folder_name_encrypted}' decrypted successfully to '{original_folder_name_decrypted}'. Encrypted folder removed.", True)


# --- GUI Classes ---
class PasswordDialog(QDialog):
    """Dialog to get master password and optionally 2FA code."""
    def __init__(self, needs_2fa=False, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Authentication")
        self.setModal(True)
        self.layout = QVBoxLayout(self)

        self.password_label = QLabel("Master Password:")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.layout.addWidget(self.password_label)
        self.layout.addWidget(self.password_input)

        self.needs_2fa = needs_2fa
        if self.needs_2fa:
            self.two_fa_label = QLabel("2FA Code (Google Authenticator):")
            self.two_fa_input = QLineEdit()
            self.two_fa_input.setPlaceholderText("6-digit code")
            self.layout.addWidget(self.two_fa_label)
            self.layout.addWidget(self.two_fa_input)

        self.buttons_layout = QHBoxLayout()
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        self.buttons_layout.addWidget(self.ok_button)
        self.buttons_layout.addWidget(self.cancel_button)
        self.layout.addLayout(self.buttons_layout)

    def get_credentials(self):
        password = self.password_input.text()
        two_fa_code = self.two_fa_input.text() if self.needs_2fa else None
        return password, two_fa_code

class FirstTimeSetupDialog(QDialog):
    """Dialog for first-time setup: master password and 2FA."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"{APP_NAME} - First Time Setup")
        self.setModal(True)
        self.layout = QVBoxLayout(self)
        self.config = load_app_config() # Load fresh config

        # Master Password Setup
        self.pwd_group = QGroupBox("1. Set Master Password")
        pwd_layout = QGridLayout()
        self.master_pwd_label = QLabel("Choose a strong master password:")
        self.master_pwd_input = QLineEdit()
        self.master_pwd_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.master_pwd_confirm_label = QLabel("Confirm password:")
        self.master_pwd_confirm_input = QLineEdit()
        self.master_pwd_confirm_input.setEchoMode(QLineEdit.EchoMode.Password)
        pwd_layout.addWidget(self.master_pwd_label, 0, 0)
        pwd_layout.addWidget(self.master_pwd_input, 0, 1)
        pwd_layout.addWidget(self.master_pwd_confirm_label, 1, 0)
        pwd_layout.addWidget(self.master_pwd_confirm_input, 1, 1)
        self.pwd_group.setLayout(pwd_layout)
        self.layout.addWidget(self.pwd_group)

        # 2FA Setup
        self.two_fa_group = QGroupBox("2. Setup Two-Factor Authentication (Recommended)")
        two_fa_layout = QVBoxLayout()
        self.qr_image_label = QLabel("Scan QR with Google Authenticator:")
        self.qr_display_label = QLabel("(QR code will appear here)") # Placeholder for QR image
        self.qr_display_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.qr_display_label.setFixedSize(250, 250)
        self.qr_display_label.setStyleSheet("border: 1px solid gray; background-color: white;")
        self.secret_key_label = QLabel("Or manually enter this key:")
        self.secret_key_value = QLineEdit()
        self.secret_key_value.setReadOnly(True)
        self.verify_2fa_label = QLabel("Enter 6-digit code from app to verify:")
        self.verify_2fa_input = QLineEdit()
        two_fa_layout.addWidget(self.qr_image_label)
        two_fa_layout.addWidget(self.qr_display_label)
        two_fa_layout.addWidget(self.secret_key_label)
        two_fa_layout.addWidget(self.secret_key_value)
        two_fa_layout.addWidget(self.verify_2fa_label)
        two_fa_layout.addWidget(self.verify_2fa_input)
        self.two_fa_group.setLayout(two_fa_layout)
        self.layout.addWidget(self.two_fa_group)
        self.generate_2fa()

        self.status_label = QLabel("")
        self.layout.addWidget(self.status_label)

        self.finish_button = QPushButton("Finish Setup")
        self.finish_button.clicked.connect(self.complete_setup)
        self.layout.addWidget(self.finish_button)

    def generate_2fa(self):
        self.current_2fa_secret = auth.generate_2fa_secret()
        self.secret_key_value.setText(self.current_2fa_secret)
        uri = auth.get_2fa_provisioning_uri(self.current_2fa_secret, f"{APP_NAME}User", APP_NAME)

        qr_img = qrcode.make(uri)
        # Convert PIL image to RGB format first, then to QPixmap
        qr_img_rgb = qr_img.convert('RGB')
        qt_image = QImage(qr_img_rgb.tobytes("raw","RGB"), qr_img_rgb.size[0], qr_img_rgb.size[1], QImage.Format.Format_RGB888)
        pixmap = QPixmap.fromImage(qt_image)
        self.qr_display_label.setPixmap(pixmap.scaled(240, 240, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))

    def complete_setup(self):
        master_pwd = self.master_pwd_input.text()
        master_pwd_confirm = self.master_pwd_confirm_input.text()
        two_fa_code_verify = self.verify_2fa_input.text()

        if not master_pwd or len(master_pwd) < 8:
            self.status_label.setText("Password must be at least 8 characters long.")
            QMessageBox.warning(self, "Password Error", "Password must be at least 8 characters long.")
            return
        if master_pwd != master_pwd_confirm:
            self.status_label.setText("Passwords do not match.")
            QMessageBox.warning(self, "Password Error", "Passwords do not match.")
            return

        if not auth.verify_2fa_code(self.current_2fa_secret, two_fa_code_verify):
            self.status_label.setText("Invalid 2FA code. Please try again.")
            QMessageBox.warning(self, "2FA Error", "Invalid 2FA code. Please ensure the code is current and correctly entered.")
            return

        # Save master password
        salt, hashed_pwd = auth.hash_password(master_pwd)
        self.config['master_password_salt'] = salt.hex()
        self.config['master_password_hash'] = hashed_pwd.hex()
        
        # Save 2FA secret
        self.config['google_2fa_secret'] = self.current_2fa_secret
        
        save_app_config(self.config)
        QMessageBox.information(self, "Setup Complete", f"{APP_NAME} has been configured successfully!")
        self.accept()

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"{APP_NAME} - v{VERSION}")
        self.setGeometry(200, 200, 600, 450)
        self.config = load_app_config()
        self.master_key_material = None # Will be derived after successful auth

        if not self.config:
            sys.exit(1) # Config failed to load, critical error

        # Check for first-time setup
        if not self.config.get('master_password_hash') or not self.config.get('google_2fa_secret'):
            setup_dialog = FirstTimeSetupDialog(self)
            if setup_dialog.exec() != QDialog.DialogCode.Accepted:
                QMessageBox.critical(self, "Setup Required", "Initial setup was not completed. Application will exit.")
                sys.exit(1)
            self.config = load_app_config() # Reload config after setup

        self.init_ui()

    def init_ui(self):
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)

        # Folder Selection
        folder_group = QGroupBox("Folder Operations")
        folder_layout = QVBoxLayout()
        self.folder_path_label = QLabel("Selected Folder: None")
        self.browse_button = QPushButton("Browse for Folder")
        self.browse_button.clicked.connect(self.browse_folder)
        folder_layout.addWidget(self.folder_path_label)
        folder_layout.addWidget(self.browse_button)
        folder_group.setLayout(folder_layout)
        self.layout.addWidget(folder_group)

        # Actions
        actions_group = QGroupBox("Actions")
        actions_layout = QHBoxLayout()
        self.encrypt_button = QPushButton("Encrypt Folder")
        self.encrypt_button.clicked.connect(lambda: self.start_operation("encrypt"))
        self.decrypt_button = QPushButton("Decrypt Folder")
        self.decrypt_button.clicked.connect(lambda: self.start_operation("decrypt"))
        actions_layout.addWidget(self.encrypt_button)
        actions_layout.addWidget(self.decrypt_button)
        actions_group.setLayout(actions_layout)
        self.layout.addWidget(actions_group)

        # Status and Progress
        status_group = QGroupBox("Status")
        status_layout = QVBoxLayout()
        self.status_label = QLabel("Status: Idle")
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.progress_bar)
        status_group.setLayout(status_layout)
        self.layout.addWidget(status_group)

        self.selected_folder = None
        self.set_buttons_enabled(False) # Initially disabled until folder selected

    def set_buttons_enabled(self, enabled):
        self.encrypt_button.setEnabled(enabled)
        self.decrypt_button.setEnabled(enabled)

    def browse_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "Select Folder")
        if folder:
            self.selected_folder = folder
            self.folder_path_label.setText(f"Selected Folder: {os.path.basename(folder)}")
            self.status_label.setText("Status: Folder selected. Ready for action.")
            self.set_buttons_enabled(True)
            # Determine if it's an encrypted folder to enable appropriate button more intelligently
            if folder.endswith("_encrypted") and os.path.exists(os.path.join(folder, ".folder_metadata")):
                self.encrypt_button.setEnabled(False)
                self.decrypt_button.setEnabled(True)
            else:
                self.encrypt_button.setEnabled(True)
                # Decrypt button should only be enabled if it's a valid encrypted folder
                self.decrypt_button.setEnabled(False) # Default to false, enable if it's an encrypted folder

    def perform_authentication(self):
        """Handles the full authentication flow (password, 2FA, USB)."""
        if auth.check_if_locked():
            QMessageBox.critical(self, "Application Locked", 
                                 f"The application is locked due to {self.config.get('max_attempts', 3)} failed login attempts.")
            return False

        # USB Key Check (if configured)
        usb_key_file = self.config.get('usb_key_filename')
        if usb_key_file:
            self.status_label.setText("Status: Checking for USB key...")
            QApplication.processEvents() # Update GUI
            if not usb_key.is_usb_key_present():
                QMessageBox.warning(self, "USB Key Not Found", 
                                    f"Required USB key file ('{usb_key_file}') not found. Please insert the correct USB drive.")
                self.status_label.setText("Status: USB key check failed.")
                return False
            self.status_label.setText("Status: USB key detected.")
            QApplication.processEvents()

        # Password and 2FA Dialog
        needs_2fa = bool(self.config.get('google_2fa_secret'))
        dialog = PasswordDialog(needs_2fa=needs_2fa, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            password, two_fa_code = dialog.get_credentials()
            if not password:
                QMessageBox.warning(self, "Authentication Failed", "Password cannot be empty.")
                return False
            
            # Authenticate user (auth.py handles attempt counting and locking)
            if auth.authenticate_user(password, two_fa_code):
                # Derive master key material for encryption/decryption from the password
                # This is a simplification. In a real high-security app, the password might unlock a stored key.
                # For now, we use a KDF on the password directly for crypto operations.
                # The salt for this KDF should be fixed or stored to ensure consistent key derivation.
                # Using a fixed salt for deriving the actual encryption key from password:
                app_salt = b'SecureFolderProAppSaltFixed12345'
                self.master_key_material = crypto_utils.generate_key_from_password(password, app_salt)
                self.status_label.setText("Status: Authentication successful.")
                return True
            else:
                # auth.authenticate_user already prints details and handles locking
                QMessageBox.warning(self, "Authentication Failed", "Invalid credentials or 2FA code.")
                # Send email alert if configured
                if self.config.get('email_config', {}).get('smtp_server'):
                     notifier.notify_failed_login_attempt(username_attempted="N/A_GUI_Attempt") # Add more info if available
                self.status_label.setText("Status: Authentication failed.")
                if auth.check_if_locked(): # Re-check if now locked
                    QMessageBox.critical(self, "Application Locked", 
                                         f"The application is now locked due to too many failed attempts.")
                    self.set_buttons_enabled(False) # Disable main actions if locked
                    self.browse_button.setEnabled(False)
                return False
        else:
            self.status_label.setText("Status: Authentication cancelled.")
            return False # User cancelled dialog

    def start_operation(self, operation_type):
        if not self.selected_folder:
            QMessageBox.warning(self, "No Folder Selected", "Please select a folder first.")
            return

        if not self.perform_authentication():
            return # Authentication failed or was cancelled

        # Disable buttons during operation
        self.set_buttons_enabled(False)
        self.browse_button.setEnabled(False)
        self.progress_bar.setValue(0)
        self.status_label.setText(f"Status: Starting {operation_type}...")

        self.thread = EncryptDecryptThread(self.selected_folder, self.master_key_material, operation_type, self.config)
        self.thread.progress.connect(self.update_progress)
        self.thread.finished.connect(self.operation_finished)
        self.thread.error.connect(self.operation_error)
        self.thread.start()

    def update_progress(self, value):
        self.progress_bar.setValue(value)
        self.status_label.setText(f"Status: Processing... {value}%")

    def operation_finished(self, message, is_success):
        self.progress_bar.setValue(100 if is_success else self.progress_bar.value())
        self.status_label.setText(f"Status: {message}")
        if is_success:
            QMessageBox.information(self, "Operation Complete", message)
            self.folder_path_label.setText("Selected Folder: None") # Reset selection
            self.selected_folder = None
        else:
            QMessageBox.warning(self, "Operation Note", message)
        
        # Re-enable buttons
        self.browse_button.setEnabled(True)
        # self.set_buttons_enabled(True) # Don't re-enable action buttons until new folder selected
        self.master_key_material = None # Clear sensitive key material
        auth.reset_failed_attempts() # Reset on any successful operation completion dialog

    def operation_error(self, error_message):
        self.progress_bar.setValue(self.progress_bar.value()) # Keep current progress or set to error state
        self.status_label.setText(f"Status: Error - {error_message}")
        QMessageBox.critical(self, "Operation Error", error_message)
        # Re-enable buttons
        self.browse_button.setEnabled(True)
        # self.set_buttons_enabled(True) # Potentially allow retry or new selection
        self.master_key_material = None # Clear sensitive key material

    def closeEvent(self, event):
        # Clean up sensitive data if any
        self.master_key_material = None
        # print("Closing application, master key cleared.")
        super().closeEvent(event)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    # Set app icon (optional, create an icon.png or icon.ico)
    # app_icon = QIcon("icon.png") 
    # if not app_icon.isNull():
    #     app.setWindowIcon(app_icon)
    
    # Ensure config.json exists, or create a very basic one if absolutely necessary
    # The FirstTimeSetupDialog should handle most of this, but a fallback:
    if not os.path.exists(CONFIG_FILE):
        print(f"{CONFIG_FILE} not found. Attempting to create a minimal one.")
        # This is a last resort. The setup dialog is preferred.
        try:
            with open(CONFIG_FILE, 'w') as f:
                json.dump({
                    "max_attempts": 3,
                    "encryption_buffer_size": 65536,
                    "usb_key_filename": "", # Empty means USB key auth disabled by default
                    "google_2fa_secret": "", # Will trigger 2FA setup
                    "email_config": { # Basic email config, user needs to fill
                        "smtp_server": "", "smtp_port": 0, 
                        "sender_email": "", "sender_password": "", 
                        "recipient_email": ""
                    },
                    "app_locked": False,
                    "failed_attempts": 0
                }, f, indent=4)
            print(f"Minimal {CONFIG_FILE} created. First-time setup will guide the user.")
        except Exception as e:
            print(f"CRITICAL: Could not create {CONFIG_FILE}: {e}. Application cannot run.")
            sys.exit(1)

    main_window = MainWindow()
    main_window.show()
    sys.exit(app.exec())