"""
USB Key Detection Module for SecureFolder Pro

Detects the presence of a specific file on a connected USB drive.
Uses psutil to list drives and check for the file.
"""

import os
import psutil # For listing drives/partitions
import time
import json

CONFIG_FILE = 'config.json'

def load_usb_key_filename() -> str | None:
    """Loads the target USB key filename from config.json."""
    try:
        with open(CONFIG_FILE, 'r') as f:
            config = json.load(f)
            return config.get('usb_key_filename')
    except FileNotFoundError:
        print(f"Error: {CONFIG_FILE} not found.")
        return None
    except json.JSONDecodeError:
        print(f"Error: {CONFIG_FILE} is corrupted.")
        return None

def find_usb_key_file(key_filename: str) -> str | None:
    """Scans removable drives for the specified key file.
    Returns the path to the key file if found, otherwise None.
    """
    if not key_filename:
        # print("USB key filename not configured.")
        return None

    # mounted_partitions = psutil.disk_partitions(all=False) # all=False typically means only 'ready' drives
    # On Windows, psutil.disk_partitions() lists all partitions, including fixed ones.
    # We need to identify removable drives.
    # A common way is to check mount options or drive types, but psutil's direct support varies by OS.

    # For Windows, drive types can sometimes be inferred from mount options or by checking drive letters
    # associated with removable media. 'opts' field in psutil.disk_partitions might contain 'removable'.
    # However, this is not universally reliable.

    # A more robust cross-platform approach might involve platform-specific libraries or commands,
    # but for simplicity with psutil, we'll iterate and check.
    # We can also filter by common USB drive mount points if known (e.g., /media/ on Linux, D:, E:, etc. on Windows)

    # Let's iterate through all partitions and check if they are likely removable
    # and if the key file exists.
    for partition in psutil.disk_partitions(all=False):
        # print(f"Checking partition: {partition.device} ({partition.mountpoint}) - type: {partition.fstype}, opts: {partition.opts}")
        # Heuristic for removable drives (can be improved):
        # - On Windows, 'removable' might be in opts, or drive letters like D:, E:, F: etc. (excluding C:)
        # - On Linux, often mounted under /media/ or /mnt/
        # - On macOS, often under /Volumes/
        
        # A simple check: if it's not the system drive (usually C: on Windows or / on Linux/macOS)
        # and it's readable.
        # This is a very basic heuristic and might include non-USB external drives.
        
        is_system_drive = False
        if os.name == 'nt':
            # System drive is usually where Windows is installed, often C:
            # partition.mountpoint will be like 'C:\', 'D:\'
            if partition.mountpoint.upper().startswith(os.getenv('SystemDrive', 'C:').upper()):
                is_system_drive = True
        else: # Linux/macOS
            if partition.mountpoint == '/':
                is_system_drive = True
        
        # Skip system drive and drives with no mountpoint (e.g. CD-ROM with no disc)
        if is_system_drive or not partition.mountpoint:
            continue

        # More specific checks for removable (can be platform dependent)
        # if 'removable' not in partition.opts.lower() and 'cdrom' not in partition.opts.lower():
            # if os.name == 'nt' and not partition.device.startswith(('D:','E:','F:','G:','H:')):
                # continue # Skip non-typical USB drive letters on Windows if opts doesn't say removable
        
        try:
            # Construct the full path to the potential key file
            potential_key_file_path = os.path.join(partition.mountpoint, key_filename)
            # print(f"  Checking for: {potential_key_file_path}")
            if os.path.exists(potential_key_file_path) and os.path.isfile(potential_key_file_path):
                # print(f"  Key file found at: {potential_key_file_path}")
                return potential_key_file_path
        except OSError as e:
            # Handles cases like 'access denied' or drive not ready, e.g. empty card reader
            # print(f"  OSError when accessing {partition.mountpoint}: {e}")
            continue
        except Exception as e:
            # print(f"  Unexpected error checking {partition.mountpoint}: {e}")
            continue
            
    return None

def is_usb_key_present() -> bool:
    """Checks if the configured USB key file is present on any connected drive."""
    key_filename = load_usb_key_filename()
    if not key_filename:
        # print("USB key feature not configured or filename missing in config.")
        return True # If not configured, effectively bypasses this check

    # print(f"Searching for USB key file: {key_filename}")
    if find_usb_key_file(key_filename):
        # print("USB key detected.")
        return True
    else:
        # print("USB key not detected.")
        return False

if __name__ == '__main__':
    print("--- USB Key Detection Test ---")

    # Ensure config.json exists with a usb_key_filename for testing
    if not os.path.exists(CONFIG_FILE):
        print(f"{CONFIG_FILE} not found. Please create it with a 'usb_key_filename' entry.")
        print("Example: {\"usb_key_filename\": \"my_secure_token.key\"}")
    else:
        # Create a dummy key file on a simulated USB path for testing (manual step)
        # For example, if you have a USB drive mounted as E:, create E:\my_secure_token.key
        # Or, for non-Windows, adjust the path accordingly.
        
        key_file_to_check = load_usb_key_filename()
        if not key_file_to_check:
            print("Please set 'usb_key_filename' in config.json to run the test.")
        else:
            print(f"Looking for USB key file: '{key_file_to_check}'")
            print("Please ensure a USB drive is connected and contains this file for a successful test.")
            print("Scanning for USB key (may take a moment)...")
            
            # Give some time for drives to be recognized if recently plugged in
            # time.sleep(2)

            found_path = find_usb_key_file(key_file_to_check)
            if found_path:
                print(f"SUCCESS: USB key file found at: {found_path}")
            else:
                print(f"FAILURE: USB key file '{key_file_to_check}' not found on any accessible removable drive.")
            
            print("\n--- Test with is_usb_key_present() function ---")
            if is_usb_key_present():
                print("is_usb_key_present() returned: True (Key likely found or feature not configured)")
            else:
                print("is_usb_key_present() returned: False (Key not found and feature is configured)")

    print("""\nNote: This test relies on `psutil` correctly identifying drives and file system access.
    Effectiveness may vary based on OS and permissions.
    Ensure the `usb_key_filename` in `config.json` matches a file on your test USB drive.""")