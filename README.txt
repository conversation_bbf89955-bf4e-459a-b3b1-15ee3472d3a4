SecureFolder Pro - README

## Description
SecureFolder Pro is a Python application designed to protect your sensitive folders with strong AES-256 encryption. It offers multiple layers of security including password protection, Google Authenticator 2FA, and USB key authentication.

## Features
- Encrypt/decrypt folder contents using AES-256.
- Obfuscate filenames for an extra layer of security.
- Lock and hide folders using system-specific commands.
- Master password protection with configurable retry limits.
- Two-Factor Authentication (2FA) using Google Authenticator.
- USB key authentication: requires a specific file on a USB drive for access.
- Email alerts for unauthorized access attempts.
- Clean and responsive Graphical User Interface (GUI).
- Option to compile into a standalone executable using PyInstaller.

## Prerequisites
- Python 3.7+ (https://www.python.org/downloads/)
- pip (Python package installer)

## Installation
1.  **Clone the repository or download the source code:**
    ```bash
    git clone <repository_url>  # Or download and extract the ZIP file
    cd SecureFolderPro
    ```

2.  **Install required Python libraries:**
    Open a terminal or command prompt in the project directory and run:
    ```bash
    pip install -r requirements.txt
    ```

## Configuration
Before running the application for the first time, configure the settings in `config.json`:

1.  **`max_attempts`**: Maximum number of incorrect password attempts before the application locks.
2.  **`encryption_buffer_size`**: Buffer size for file encryption/decryption (e.g., 65536 for 64KB).
3.  **`usb_key_filename`**: The name of the file the application will look for on a USB drive (e.g., `secure_key.dat`). Create an empty file with this name on your USB drive.
4.  **`google_2fa_secret`**: 
    *   If you have a secret key from Google Authenticator, enter it here.
    *   If left empty or not present, the application will generate a new secret on first run. You will be shown a QR code to scan with the Google Authenticator app.
5.  **Email Alert Configuration (`email_config`):**
    *   `smtp_server`: Your SMTP server address (e.g., "smtp.gmail.com").
    *   `smtp_port`: SMTP server port (e.g., 587 for TLS, 465 for SSL).
    *   `sender_email`: Your email address for sending alerts.
    *   `sender_password`: Your email password or app-specific password. **(Ensure this is handled securely and consider using environment variables or a dedicated secrets management solution for production scenarios).**
    *   `recipient_email`: Email address to receive the alerts.

## Usage
1.  **Run the application:**
    ```bash
    python main.py
    ```

2.  **First-Time Setup (if 2FA secret is not in `config.json`):**
    *   A QR code will be displayed. Scan it with your Google Authenticator app.
    *   Enter the 6-digit code from the app to complete the setup.

3.  **GUI Operations:**
    *   **Browse Folder**: Select the folder you want to encrypt or decrypt.
    *   **Encrypt**: Enter your master password, 2FA code (if enabled), and ensure your USB key (if configured) is connected. Click "Encrypt". The folder will be encrypted, and its original version will be hidden or moved.
    *   **Decrypt**: Enter your master password, 2FA code (if enabled), and ensure your USB key (if configured) is connected. Click "Decrypt". The folder will be decrypted and restored.
    *   **Status**: The GUI will display the current status of operations.

## Creating a Standalone Executable (Optional)
To create a standalone `.exe` file (on Windows):

1.  **Install PyInstaller (if not already installed):**
    ```bash
    pip install pyinstaller
    ```

2.  **Build the executable:**
    Navigate to the project directory in your terminal and run:
    ```bash
    pyinstaller --onefile --windowed --name SecureFolderPro main.py
    ```
    *   `--onefile`: Creates a single executable file.
    *   `--windowed`: Prevents the console window from appearing when the GUI application runs.
    *   `--name SecureFolderPro`: Sets the name of the output executable.

    The executable will be found in the `dist` folder within your project directory.

## Security Notes
*   **Master Password**: Choose a strong, unique password. Do not share it.
*   **2FA**: Enable 2FA for an added layer of security.
*   **USB Key**: Keep your USB key in a safe place.
*   **Email Credentials**: Be cautious about storing email passwords directly in `config.json` for production use. Consider more secure alternatives.
*   **Backup**: Always back up important data before encrypting it, especially when using new encryption software.

## Disclaimer
This software is provided "as is" without warranty of any kind. The authors are not responsible for any data loss or damage that may occur from its use. Use at your own risk.