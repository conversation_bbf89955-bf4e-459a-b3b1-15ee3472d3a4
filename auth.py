"""
Authentication Module for SecureFolder Pro

Manages master password authentication (hashing, verification), 
Google Authenticator 2FA, and retry limits.
"""

import hashlib
import hmac
import os
import json
import time
import pyotp
import qrcode # For generating QR code imagerom PIL import Image # For displaying QR code image

CONFIG_FILE = 'config.json'

# --- Password Management ---
def hash_password(password: str, salt: bytes = None) -> tuple[bytes, bytes]:
    """Hashes a password using PBKDF2HMAC-SHA256. Returns salt and hashed password."""
    if salt is None:
        salt = os.urandom(16)
    # iterations = 200000 # Increased iterations for better security
    # For faster testing, using slightly lower, but production should be high.
    iterations = 150000 
    hashed_password = hashlib.pbkdf2_hmac(
        'sha256',
        password.encode('utf-8'),
        salt,
        iterations,
        dklen=64  # Desired key length
    )
    return salt, hashed_password

def verify_password(stored_salt: bytes, stored_hash: bytes, provided_password: str) -> bool:
    """Verifies a provided password against a stored salt and hash."""
    iterations = 150000 # Must match the iterations used for hashing
    new_hash = hashlib.pbkdf2_hmac(
        'sha256',
        provided_password.encode('utf-8'),
        stored_salt,
        iterations,
        dklen=64
    )
    return hmac.compare_digest(new_hash, stored_hash)

# --- Configuration Loading/Saving (related to auth state) ---
def load_config() -> dict:
    """Loads configuration from config.json."""
    try:
        with open(CONFIG_FILE, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        # This should ideally not happen if config.json is created first
        # Or, create a default config here if it's missing
        print(f"Error: {CONFIG_FILE} not found. Please ensure it exists.")
        return {}
    except json.JSONDecodeError:
        print(f"Error: {CONFIG_FILE} is corrupted or not valid JSON.")
        return {}

def save_config(config_data: dict):
    """Saves configuration to config.json."""
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config_data, f, indent=4)
    except IOError:
        print(f"Error: Could not write to {CONFIG_FILE}.")

# --- 2FA Management ---
def generate_2fa_secret() -> str:
    """Generates a new base32 secret key for 2FA."""
    return pyotp.random_base32()

def get_2fa_provisioning_uri(secret: str, username: str, issuer_name: str = "SecureFolderPro") -> str:
    """Generates a provisioning URI for Google Authenticator."""
    return pyotp.totp.TOTP(secret).provisioning_uri(name=username, issuer_name=issuer_name)

def verify_2fa_code(secret: str, code: str) -> bool:
    """Verifies a 2FA code."""
    totp = pyotp.TOTP(secret)
    return totp.verify(code)

def display_qr_code(uri: str, temp_qr_file: str = "temp_qr.png"):
    """Generates and displays a QR code for 2FA setup."""
    img = qrcode.make(uri)
    img.save(temp_qr_file)
    print(f"QR code saved to {temp_qr_file}. Please scan it with Google Authenticator.")
    try:
        # Try to open the image using the default system viewer
        if os.name == 'nt': # Windows
            os.startfile(temp_qr_file)
        elif os.name == 'posix': # macOS, Linux
            # Try common commands
            if os.system(f'open "{temp_qr_file}"') != 0: # macOS
                if os.system(f'xdg-open "{temp_qr_file}"') != 0: # Linux (freedesktop)
                    print(f"Could not automatically open {temp_qr_file}. Please open it manually.")
        else:
            print(f"Could not automatically open {temp_qr_file} on this OS. Please open it manually.")
        
        # Keep the temp file for a bit so the user can see it if auto-open fails
        # In a GUI app, you'd display this in the GUI and delete on close/confirm
    except Exception as e:
        print(f"Error displaying QR code: {e}. Please open {temp_qr_file} manually.")

# --- Attempt Tracking and Locking ---
def check_if_locked() -> bool:
    """Checks if the application is currently locked due to too many failed attempts."""
    config = load_config()
    return config.get('app_locked', False)

def record_failed_attempt() -> bool:
    """Records a failed login attempt and locks the app if max attempts are exceeded.
    Returns True if the app is now locked, False otherwise.
    """
    config = load_config()
    if config.get('app_locked', False):
        return True # Already locked

    max_attempts = config.get('max_attempts', 3)
    failed_attempts = config.get('failed_attempts', 0) + 1
    config['failed_attempts'] = failed_attempts

    if failed_attempts >= max_attempts:
        config['app_locked'] = True
        print(f"Maximum login attempts ({max_attempts}) reached. Application is now locked.")
        # Optionally, trigger self-destruct here if configured
        # if config.get('enable_self_destruct', False):
        #     trigger_self_destruct() # This function would need to be implemented
        save_config(config)
        return True
    
    save_config(config)
    return False

def reset_failed_attempts():
    """Resets the failed attempt counter and unlocks the app if it was locked."""
    config = load_config()
    config['failed_attempts'] = 0
    if config.get('app_locked', False):
        config['app_locked'] = False
        print("Application unlocked.")
    save_config(config)

# --- Master Password Setup (called once, typically) ---
def setup_master_password(password: str):
    """Sets up the master password by hashing and storing it.
       This should be called during initial application setup.
    """
    config = load_config()
    if 'master_password_salt' in config and 'master_password_hash' in config:
        print("Master password already set. To change, use a dedicated function (not implemented here for safety).")
        return

    salt, hashed_pwd = hash_password(password)
    config['master_password_salt'] = salt.hex() # Store as hex string
    config['master_password_hash'] = hashed_pwd.hex() # Store as hex string
    save_config(config)
    print("Master password has been set successfully.")

# --- Main Authentication Flow ---
def authenticate_user(password: str, two_fa_code: str = None) -> bool:
    """Authenticates the user using master password and optionally 2FA.
       Returns True if authentication is successful, False otherwise.
    """
    if check_if_locked():
        print("Application is locked due to too many failed attempts.")
        return False

    config = load_config()
    salt_hex = config.get('master_password_salt')
    hash_hex = config.get('master_password_hash')

    if not salt_hex or not hash_hex:
        print("Master password not set. Please run initial setup.")
        # In a real app, this might trigger the setup flow
        return False

    salt = bytes.fromhex(salt_hex)
    stored_hash = bytes.fromhex(hash_hex)

    if not verify_password(salt, stored_hash, password):
        print("Invalid master password.")
        record_failed_attempt()
        # Potentially trigger email alert here via notifier.py
        return False

    # Password is correct, now check 2FA if enabled/configured
    secret_2fa = config.get('google_2fa_secret')
    if secret_2fa: # 2FA is configured
        if two_fa_code is None:
            print("2FA code required but not provided.")
            # Don't record as a full failed attempt here, as password was right.
            # GUI should ensure 2FA code is asked for if configured.
            return False 
        if not verify_2fa_code(secret_2fa, two_fa_code):
            print("Invalid 2FA code.")
            record_failed_attempt() # Count as a failed attempt if 2FA fails
            # Potentially trigger email alert here
            return False
        print("2FA verification successful.")

    # All checks passed
    reset_failed_attempts() # Successful login, reset counter
    print("Authentication successful.")
    return True


if __name__ == '__main__':
    # Example Usage (for testing - requires config.json to exist)
    
    # --- Initial Setup (run once) ---
    # Ensure config.json exists. If running this script directly, create a dummy one if needed.
    if not os.path.exists(CONFIG_FILE):
        initial_cfg = {
            "max_attempts": 3,
            "encryption_buffer_size": 65536,
            "usb_key_filename": "secure_key.dat",
            "google_2fa_secret": "", # Will be generated
            "email_config": {"smtp_server": "", "smtp_port": 0, "sender_email": "", "sender_password": "", "recipient_email": ""},
            "app_locked": False,
            "failed_attempts": 0
        }
        save_config(initial_cfg)
        print(f"{CONFIG_FILE} created with default values.")

    # 1. Set Master Password (if not already set)
    cfg_test = load_config()
    if not cfg_test.get('master_password_hash'):
        print("\n--- Setting up Master Password ---")
        setup_master_password("StrongPassword123!")
    else:
        print("\n--- Master Password Already Set ---")

    # 2. Setup 2FA (if not already set)
    cfg_test = load_config() # Reload config
    if not cfg_test.get('google_2fa_secret'):
        print("\n--- Setting up 2FA ---")
        new_secret = generate_2fa_secret()
        cfg_test['google_2fa_secret'] = new_secret
        save_config(cfg_test)
        
        uri = get_2fa_provisioning_uri(new_secret, "<EMAIL>")
        print(f"Scan this URI with Google Authenticator: {uri}")
        # In a real app, you'd show a QR code.
        display_qr_code(uri, "test_2fa_qr.png")
        print("2FA secret generated and saved. QR code displayed (if possible).")
        # Wait for user to scan and enter a code for verification (manual step for this test)
        # For automated test, you'd need to use pyotp to generate a code from the secret.
    else:
        print("\n--- 2FA Already Set Up ---")
        print(f"2FA Secret (for testing): {cfg_test.get('google_2fa_secret')}")

    # --- Test Authentication ---
    print("\n--- Testing Authentication ---")
    
    # Simulate getting a 2FA code (if 2FA is set up)
    current_2fa_code = None
    cfg_for_auth = load_config()
    if cfg_for_auth.get('google_2fa_secret'):
        totp_obj = pyotp.TOTP(cfg_for_auth['google_2fa_secret'])
        current_2fa_code = totp_obj.now()
        print(f"Current 2FA code (for testing): {current_2fa_code}")

    # Test correct login
    print("\nAttempting correct login...")
    if authenticate_user("StrongPassword123!", current_2fa_code):
        print("Login test successful.")
    else:
        print("Login test FAILED.")

    # Test incorrect password
    print("\nAttempting incorrect password...")
    if not authenticate_user("WrongPassword", current_2fa_code):
        print("Incorrect password test successful (login failed as expected).")
    else:
        print("Incorrect password test FAILED (login succeeded unexpectedly).")
    
    # Test incorrect 2FA (if 2FA is enabled)
    if cfg_for_auth.get('google_2fa_secret'):
        print("\nAttempting incorrect 2FA code...")
        if not authenticate_user("StrongPassword123!", "000000"): # Invalid 2FA
            print("Incorrect 2FA test successful (login failed as expected).")
        else:
            print("Incorrect 2FA test FAILED (login succeeded unexpectedly).")

    # Test locking mechanism
    print("\n--- Testing Account Locking ---")
    reset_failed_attempts() # Ensure clean state
    max_att = cfg_for_auth.get('max_attempts', 3)
    for i in range(max_att):
        print(f"Failed attempt {i+1}/{max_att}...")
        authenticate_user("WrongPasswordAgain", "123456") # Will fail
        if check_if_locked():
            print(f"App locked after {i+1} attempts.")
            break
    
    if check_if_locked():
        print("Locking mechanism test successful.")
        # Try to login while locked
        if not authenticate_user("StrongPassword123!", current_2fa_code):
            print("Attempt to login while locked failed (as expected).")
        else:
            print("Attempt to login while locked SUCCEEDED (ERROR!).")
    else:
        print("Locking mechanism test FAILED (app not locked after max attempts).")

    # Reset for next run if needed
    # reset_failed_attempts() 
    # To fully reset for a new test run, you might need to delete config.json or parts of it.
    # For example, to re-test 2FA setup, remove 'google_2fa_secret' from config.json.
    # To re-test password setup, remove 'master_password_salt' and 'master_password_hash'.

    # Clean up temp QR file
    if os.path.exists("test_2fa_qr.png"):
        try:
            # Add a small delay to allow the OS to release the file if it was opened
            time.sleep(1)
            os.remove("test_2fa_qr.png")
            print("Cleaned up temporary QR code image.")
        except PermissionError:
            print("Could not delete temporary QR code image. It might be in use.")
        except Exception as e:
            print(f"Error cleaning up QR code: {e}")

    print("\nAuth module tests finished.")